package com.sh.game.common.communication.msg.system.equipCollect;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>返回装备收集信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResEquipCollectInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.EquipCollectProtos.ResEquipCollectInfo proto;

    private com.sh.game.protos.EquipCollectProtos.ResEquipCollectInfo.Builder builder;

	
	@Override
	public int getId() {
		return 319011;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.EquipCollectProtos.ResEquipCollectInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.EquipCollectProtos.ResEquipCollectInfo.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.EquipCollectProtos.ResEquipCollectInfo.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.EquipCollectProtos.ResEquipCollectInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.EquipCollectProtos.ResEquipCollectInfo proto) {
        this.proto = proto;
    }

}
