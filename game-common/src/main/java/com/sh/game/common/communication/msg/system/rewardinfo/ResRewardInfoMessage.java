package com.sh.game.common.communication.msg.system.rewardinfo;

import com.google.protobuf.InvalidProtocolBufferException;
import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;


/**
 * <p>返回奖励信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResRewardInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.RewardInfoProtos.ResRewardInfoMessage proto;

    private com.sh.game.protos.RewardInfoProtos.ResRewardInfoMessage.Builder builder;

	
	@Override
	public int getId() {
		return 397001;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.RewardInfoProtos.ResRewardInfoMessage.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.RewardInfoProtos.ResRewardInfoMessage.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.RewardInfoProtos.ResRewardInfoMessage.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.RewardInfoProtos.ResRewardInfoMessage getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.RewardInfoProtos.ResRewardInfoMessage proto) {
        this.proto = proto;
    }

}
