package com.sh.game.common.communication.msg.system.militaryRank;

import com.sh.game.common.communication.msg.system.task.bean.TaskDataBean;
import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>返回军衔信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toClient")
public class ResUnionCampInfoMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 366002;
	}
	
	/**
	 * 军衔等级
	 */
	private int level;
	/**
	 * 正在军衔任务信息
	 */
	private List<TaskDataBean> taskBean = new ArrayList<>();
	/**
	 * 是否领取过每日俸禄
	 */
	private boolean isRequired;

	public int getLevel() {
		return level;
	}

	public void setLevel(int level) {
		this.level = level;
	}

		public List<TaskDataBean> getTaskBean() {
		return taskBean;
	}

	public void setTaskBean(List<TaskDataBean> taskBean) {
		this.taskBean = taskBean;
	}
	public boolean getIsRequired() {
		return isRequired;
	}

	public void setIsRequired(boolean isRequired) {
		this.isRequired = isRequired;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.level = readInt(buf, false);
		int taskBeanLength = readShort(buf);
		for (int taskBeanI = 0; taskBeanI < taskBeanLength; taskBeanI++) {
			if (readByte(buf) == 0) { 
				this.taskBean.add(null);
			} else {
				TaskDataBean taskDataBean = new TaskDataBean();
				taskDataBean.read(buf);
				this.taskBean.add(taskDataBean);
			}
		}
		this.isRequired = readBoolean(buf);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, level, false);
		writeShort(buf, this.taskBean.size());
		for (int taskBeanI = 0; taskBeanI < this.taskBean.size(); taskBeanI++) {
			this.writeBean(buf, this.taskBean.get(taskBeanI));
		}
		this.writeBoolean(buf, isRequired);
		return true;
	}
}
