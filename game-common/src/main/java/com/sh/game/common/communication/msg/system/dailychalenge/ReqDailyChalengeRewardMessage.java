package com.sh.game.common.communication.msg.system.dailychalenge;

import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求领取奖励</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqDailyChalengeRewardMessage extends ProtobufMessage {

    private com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeReward proto;

    private com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeReward.Builder builder;

	
	@Override
	public int getId() {
		return 306005;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeReward.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeReward.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeReward.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeReward getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeReward proto) {
        this.proto = proto;
    }

}
