package com.sh.game.common.communication.msg.system.faqihuiyuan;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>查询当前法器会员等级</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResFaQiHuiYuanUpgradeMessage extends ProtobufMessage {

    private com.sh.game.protos.FaqihuiyuanProtos.ResFaQiHuiYuanUpgrade proto;

    private com.sh.game.protos.FaqihuiyuanProtos.ResFaQiHuiYuanUpgrade.Builder builder;

	
	@Override
	public int getId() {
		return 337004;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.FaqihuiyuanProtos.ResFaQiHuiYuanUpgrade.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.FaqihuiyuanProtos.ResFaQiHuiYuanUpgrade.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.FaqihuiyuanProtos.ResFaQiHuiYuanUpgrade.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.FaqihuiyuanProtos.ResFaQiHuiYuanUpgrade getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.FaqihuiyuanProtos.ResFaQiHuiYuanUpgrade proto) {
        this.proto = proto;
    }

}
