package com.sh.game.common.communication.notice.logic.player;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 神兵等级升级 notice
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2021-08-02
 **/

@Notice
@Getter
@Setter
public class ShenBingVipLevelUpdateToSceneNotice extends ProcessNotice {

    /**
     * 玩家编号
     */
    private long roleId;

    /**
     * 神兵会员等级
     */
    private int curShenBingVipLevel;

    /**
     * 已解锁神兵
     */
    private List<Integer> requiredShenBingIds;

    public ShenBingVipLevelUpdateToSceneNotice() {
    }

    public ShenBingVipLevelUpdateToSceneNotice(long roleId, int curShenBingVipLevel, List<Integer> requiredShenBingIds) {
        this.roleId = roleId;
        this.curShenBingVipLevel = curShenBingVipLevel;
        this.requiredShenBingIds = requiredShenBingIds;
    }
}
