package com.sh.game.common.communication.msg.system.shenWeiTuPo;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>返回神威信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResShenWeiInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.ShenWeiTuPoProtos.ResShenWeiInfo proto;

    private com.sh.game.protos.ShenWeiTuPoProtos.ResShenWeiInfo.Builder builder;

	
	@Override
	public int getId() {
		return 329001;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ShenWeiTuPoProtos.ResShenWeiInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ShenWeiTuPoProtos.ResShenWeiInfo.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ShenWeiTuPoProtos.ResShenWeiInfo.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ShenWeiTuPoProtos.ResShenWeiInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ShenWeiTuPoProtos.ResShenWeiInfo proto) {
        this.proto = proto;
    }

}
