package com.sh.game.common.communication.msg.system.chengzhangjijin;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>领取成功则返回领取奖励情况</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResChengZhangJiJinRewardMessage extends ProtobufMessage {

    private com.sh.game.protos.ChengZhangJiJinProtos.ResChengZhangJiJinReward proto;

    private com.sh.game.protos.ChengZhangJiJinProtos.ResChengZhangJiJinReward.Builder builder;

	
	@Override
	public int getId() {
		return 324004;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ChengZhangJiJinProtos.ResChengZhangJiJinReward.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ChengZhangJiJinProtos.ResChengZhangJiJinReward.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ChengZhangJiJinProtos.ResChengZhangJiJinReward.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ChengZhangJiJinProtos.ResChengZhangJiJinReward getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ChengZhangJiJinProtos.ResChengZhangJiJinReward proto) {
        this.proto = proto;
    }

}
