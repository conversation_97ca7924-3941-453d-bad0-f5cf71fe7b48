package com.sh.game.common.communication.notice;

import com.sh.game.common.constant.LogAction;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 物品增加 Notice
 * <AUTHOR>
 **/
@Getter
@Setter
@Notice
public class HouYuanItemAddNotice extends ProcessNotice {

    private long rid;

    private List<int[]> items = new ArrayList<>();

    private int action;

    private int self;
}

