package com.sh.game.common.communication.msg.system.faqihuiyuan;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>法器会员扫荡一次轮回塔, 成功后会自动返回轮回塔的ResQueryLunHuiTaMessage</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqFaQiHuiYuanLunHuiTaMessage extends ProtobufMessage {

    private com.sh.game.protos.FaqihuiyuanProtos.ReqFaQiHuiYuanLunHuiTa proto;

    private com.sh.game.protos.FaqihuiyuanProtos.ReqFaQiHuiYuanLunHuiTa.Builder builder;

	
	@Override
	public int getId() {
		return 337005;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.FaqihuiyuanProtos.ReqFaQiHuiYuanLunHuiTa.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.FaqihuiyuanProtos.ReqFaQiHuiYuanLunHuiTa.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.FaqihuiyuanProtos.ReqFaQiHuiYuanLunHuiTa.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.FaqihuiyuanProtos.ReqFaQiHuiYuanLunHuiTa getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.FaqihuiyuanProtos.ReqFaQiHuiYuanLunHuiTa proto) {
        this.proto = proto;
    }

}
