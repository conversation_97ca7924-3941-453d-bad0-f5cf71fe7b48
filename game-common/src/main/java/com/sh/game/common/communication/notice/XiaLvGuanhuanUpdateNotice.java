package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

@Notice
@Getter
@Setter
public class XiaLvGuanhuanUpdateNotice extends ProcessNotice {

    /**
     * 玩家编号
     */
    private long roleId;

    /**
     * 侠侣光环配置ID
     */
    private int guanghuanId;

    public XiaLvGuanhuanUpdateNotice() {
    }

    public XiaLvGuanhuanUpdateNotice(long roleId, int guanghuanId) {
        this.roleId = roleId;
        this.guanghuanId = guanghuanId;
    }
}
