package com.sh.game.common.communication.msg.system.xiaoxiaole;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
* <p>请求消消乐游戏广告通关奖励翻倍</p>
* <p>Created by MessageUtil</p>
* <p>该类是自动生成的，不允许手动修改</p>
*/
@RPC("toLogic")
public class ReqXiaoXiaoLeAdvertiseRewardMessage extends ProtobufMessage {

    private com.sh.game.protos.XiaoXiaoLeProtos.ReqXiaoXiaoLeAdvertiseRewardMessage proto;

    private com.sh.game.protos.XiaoXiaoLeProtos.ReqXiaoXiaoLeAdvertiseRewardMessage.Builder builder;


    @Override
    public int getId() {
        return 510005;
    }

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.XiaoXiaoLeProtos.ReqXiaoXiaoLeAdvertiseRewardMessage.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.XiaoXiaoLeProtos.ReqXiaoXiaoLeAdvertiseRewardMessage.Builder newBuilder() {
        if (builder == null) {
            builder = com.sh.game.protos.XiaoXiaoLeProtos.ReqXiaoXiaoLeAdvertiseRewardMessage.newBuilder();
        }
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.XiaoXiaoLeProtos.ReqXiaoXiaoLeAdvertiseRewardMessage getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.XiaoXiaoLeProtos.ReqXiaoXiaoLeAdvertiseRewardMessage proto) {
        this.proto = proto;
    }

}
