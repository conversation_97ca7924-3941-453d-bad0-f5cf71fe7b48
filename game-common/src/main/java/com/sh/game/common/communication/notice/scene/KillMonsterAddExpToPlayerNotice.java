package com.sh.game.common.communication.notice.scene;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 神兵特权额外经验 Notice
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2021-08-09
 **/
@Getter
@Setter
@Notice
@NoArgsConstructor
@AllArgsConstructor
public class KillMonsterAddExpToPlayerNotice extends ProcessNotice {
    /**
     * 击杀者 id
     */
    private long roleId;

    /**
     * 击杀怪物增加的经验(累加后的值)
     */
    private long sumExp;

    /**
     * 被击杀怪物cid
     */
    private int monsterCid;

    /**
     * 怪物死亡点坐标x
     */
    private int pointX;

    /**
     * 怪物死亡点坐标Y
     */
    private int pointY;
}
