package com.sh.game.common.communication.msg.system.zuiqiangxiuxing;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求玩家信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqMyInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.ZuiQiangXiuXingProtos.ReqMyInfoMessage proto;

    private com.sh.game.protos.ZuiQiangXiuXingProtos.ReqMyInfoMessage.Builder builder;

	
	@Override
	public int getId() {
		return 410006;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ZuiQiangXiuXingProtos.ReqMyInfoMessage.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ZuiQiangXiuXingProtos.ReqMyInfoMessage.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ZuiQiangXiuXingProtos.ReqMyInfoMessage.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ZuiQiangXiuXingProtos.ReqMyInfoMessage getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ZuiQiangXiuXingProtos.ReqMyInfoMessage proto) {
        this.proto = proto;
    }

}
