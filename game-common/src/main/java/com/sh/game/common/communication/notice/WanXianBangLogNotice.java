package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/21 16:14
 */
@Getter
@Setter
@Notice
public class WanXianBangLogNotice extends ProcessNotice {

    private long attackerId;

    private String attackerName;

    private int attackRank;

    private int attackLevel;

    private long defendId;

    private String defendName;

    private int defendRank;

    private int defendLevel;

    private boolean attackerWin;

    private long time;
}
