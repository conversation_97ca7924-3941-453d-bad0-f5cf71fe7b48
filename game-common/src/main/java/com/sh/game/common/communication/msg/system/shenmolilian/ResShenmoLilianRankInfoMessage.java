package com.sh.game.common.communication.msg.system.shenmolilian;

import com.google.protobuf.InvalidProtocolBufferException;
import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;


/**
 * <p>返回神魔历练排行榜信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResShenmoLilianRankInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.ShenmoLilianProtos.ResShenmoLilianRankInfoMessage proto;

    private com.sh.game.protos.ShenmoLilianProtos.ResShenmoLilianRankInfoMessage.Builder builder;

	
	@Override
	public int getId() {
		return 399006;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ShenmoLilianProtos.ResShenmoLilianRankInfoMessage.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ShenmoLilianProtos.ResShenmoLilianRankInfoMessage.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ShenmoLilianProtos.ResShenmoLilianRankInfoMessage.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ShenmoLilianProtos.ResShenmoLilianRankInfoMessage getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ShenmoLilianProtos.ResShenmoLilianRankInfoMessage proto) {
        this.proto = proto;
    }

}
