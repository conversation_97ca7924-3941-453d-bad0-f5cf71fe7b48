package com.sh.game.common.communication.msg.system.caishenmiao;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
* <p>返回财神庙信息</p>
* <p>Created by MessageUtil</p>
* <p>该类是自动生成的，不允许手动修改</p>
*/
@RPC("toClient")
public class ResCaiShenMiaoInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.CaiShenMiaoProtos.ResCaiShenMiaoInfoMessage proto;

    private com.sh.game.protos.CaiShenMiaoProtos.ResCaiShenMiaoInfoMessage.Builder builder;


    @Override
    public int getId() {
        return 522002;
    }

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.CaiShenMiaoProtos.ResCaiShenMiaoInfoMessage.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.CaiShenMiaoProtos.ResCaiShenMiaoInfoMessage.Builder newBuilder() {
        if (builder == null) {
            builder = com.sh.game.protos.CaiShenMiaoProtos.ResCaiShenMiaoInfoMessage.newBuilder();
        }
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.CaiShenMiaoProtos.ResCaiShenMiaoInfoMessage getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.CaiShenMiaoProtos.ResCaiShenMiaoInfoMessage proto) {
        this.proto = proto;
    }

}
