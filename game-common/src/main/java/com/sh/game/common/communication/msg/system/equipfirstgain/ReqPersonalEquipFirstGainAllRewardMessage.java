package com.sh.game.common.communication.msg.system.equipfirstgain;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求一键获取首爆奖励</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqPersonalEquipFirstGainAllRewardMessage extends ProtobufMessage {

    private com.sh.game.protos.EquipfirstgainProtos.ReqPersonalEquipFirstGainAllReward proto;

    private com.sh.game.protos.EquipfirstgainProtos.ReqPersonalEquipFirstGainAllReward.Builder builder;

	
	@Override
	public int getId() {
		return 326001;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.EquipfirstgainProtos.ReqPersonalEquipFirstGainAllReward.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.EquipfirstgainProtos.ReqPersonalEquipFirstGainAllReward.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.EquipfirstgainProtos.ReqPersonalEquipFirstGainAllReward.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.EquipfirstgainProtos.ReqPersonalEquipFirstGainAllReward getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.EquipfirstgainProtos.ReqPersonalEquipFirstGainAllReward proto) {
        this.proto = proto;
    }

}
