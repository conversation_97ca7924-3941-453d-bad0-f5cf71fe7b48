package com.sh.game.common.communication.msg.system.faqihuiyuan;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>领取一次每日工资, 成功后返回 ResFaQiHuiYuanUpgradeMessage</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqFaQiHuiYuanDailyRewardMessage extends ProtobufMessage {

    private com.sh.game.protos.FaqihuiyuanProtos.ReqFaQiHuiYuanDailyReward proto;

    private com.sh.game.protos.FaqihuiyuanProtos.ReqFaQiHuiYuanDailyReward.Builder builder;

	
	@Override
	public int getId() {
		return 337008;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.FaqihuiyuanProtos.ReqFaQiHuiYuanDailyReward.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.FaqihuiyuanProtos.ReqFaQiHuiYuanDailyReward.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.FaqihuiyuanProtos.ReqFaQiHuiYuanDailyReward.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.FaqihuiyuanProtos.ReqFaQiHuiYuanDailyReward getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.FaqihuiyuanProtos.ReqFaQiHuiYuanDailyReward proto) {
        this.proto = proto;
    }

}
