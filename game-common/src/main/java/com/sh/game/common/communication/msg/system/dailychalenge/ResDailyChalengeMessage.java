package com.sh.game.common.communication.msg.system.dailychalenge;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>挑战信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResDailyChalengeMessage extends ProtobufMessage {

    private com.sh.game.protos.DailychalengeProtos.ResDailyChalenge proto;

    private com.sh.game.protos.DailychalengeProtos.ResDailyChalenge.Builder builder;

	
	@Override
	public int getId() {
		return 306002;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.DailychalengeProtos.ResDailyChalenge.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.DailychalengeProtos.ResDailyChalenge.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.DailychalengeProtos.ResDailyChalenge.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.DailychalengeProtos.ResDailyChalenge getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.DailychalengeProtos.ResDailyChalenge proto) {
        this.proto = proto;
    }

}
