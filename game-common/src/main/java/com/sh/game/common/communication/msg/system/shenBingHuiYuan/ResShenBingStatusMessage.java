package com.sh.game.common.communication.msg.system.shenBingHuiYuan;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


import java.util.ArrayList;
import java.util.List;

/**
 * <p>响应已激活神兵特权cfgId</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toClient")
public class ResShenBingStatusMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 200013;
	}
	
	/**
	 * 当前神兵cfgId
	 */
	private int curShenBingCfgId;
	/**
	 * 当前正在进行的神兵(待解锁) cfgId
	 */
	private int curlockShenBingCfgId;
	/**
	 * 已购买礼包cfgId
	 */
	private List<Integer> purchasedGifts = new ArrayList<>();
	/**
	 * 已领取礼包cfgId
	 */
	private List<Integer> receivedRewards = new ArrayList<>();

	public int getCurShenBingCfgId() {
		return curShenBingCfgId;
	}

	public void setCurShenBingCfgId(int curShenBingCfgId) {
		this.curShenBingCfgId = curShenBingCfgId;
	}

		public int getCurlockShenBingCfgId() {
		return curlockShenBingCfgId;
	}

	public void setCurlockShenBingCfgId(int curlockShenBingCfgId) {
		this.curlockShenBingCfgId = curlockShenBingCfgId;
	}

		public List<Integer> getPurchasedGifts() {
		return purchasedGifts;
	}

	public void setPurchasedGifts(List<Integer> purchasedGifts) {
		this.purchasedGifts = purchasedGifts;
	}
	public List<Integer> getReceivedRewards() {
		return receivedRewards;
	}

	public void setReceivedRewards(List<Integer> receivedRewards) {
		this.receivedRewards = receivedRewards;
	}

	@Override
	public boolean read(KryoInput buf) {

		this.curShenBingCfgId = readInt(buf, false);
		this.curlockShenBingCfgId = readInt(buf, false);
		int purchasedGiftsLength = readShort(buf);
		for (int purchasedGiftsI = 0; purchasedGiftsI < purchasedGiftsLength; purchasedGiftsI++) {
			this.purchasedGifts.add(this.readInt(buf, false));
		}
		int receivedRewardsLength = readShort(buf);
		for (int receivedRewardsI = 0; receivedRewardsI < receivedRewardsLength; receivedRewardsI++) {
			this.receivedRewards.add(this.readInt(buf, false));
		}
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, curShenBingCfgId, false);
		this.writeInt(buf, curlockShenBingCfgId, false);
		writeShort(buf, this.purchasedGifts.size());
		for (int purchasedGiftsI = 0; purchasedGiftsI < this.purchasedGifts.size(); purchasedGiftsI++) {
			this.writeInt(buf, this.purchasedGifts.get(purchasedGiftsI), false);
		}
		writeShort(buf, this.receivedRewards.size());
		for (int receivedRewardsI = 0; receivedRewardsI < this.receivedRewards.size(); receivedRewardsI++) {
			this.writeInt(buf, this.receivedRewards.get(receivedRewardsI), false);
		}
		return true;
	}
}
