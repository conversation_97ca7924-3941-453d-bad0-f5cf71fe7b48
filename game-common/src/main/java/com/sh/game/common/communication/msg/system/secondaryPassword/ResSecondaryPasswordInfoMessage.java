package com.sh.game.common.communication.msg.system.secondaryPassword;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>二级密码信息返回</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResSecondaryPasswordInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.SecondaryPasswordProtos.ResSecondaryPasswordInfo proto;

    private com.sh.game.protos.SecondaryPasswordProtos.ResSecondaryPasswordInfo.Builder builder;

	
	@Override
	public int getId() {
		return 280002;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.SecondaryPasswordProtos.ResSecondaryPasswordInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.SecondaryPasswordProtos.ResSecondaryPasswordInfo.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.SecondaryPasswordProtos.ResSecondaryPasswordInfo.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.SecondaryPasswordProtos.ResSecondaryPasswordInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.SecondaryPasswordProtos.ResSecondaryPasswordInfo proto) {
        this.proto = proto;
    }

}
