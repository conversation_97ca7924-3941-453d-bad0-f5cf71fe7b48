package com.sh.game.common.communication.notice.logic.player;


import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 *  玩家改变坐标
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2021/8/23.
 */
@Notice
@Getter
@Setter
public class PlayerShiftPointToSceneNotice extends ProcessNotice {

    private long roleId;

    private long mapId;

    private int x;

    private int y;

    /**
     * 增加传送随机范围
     */
    private int range;

    private int deliverId;
}
