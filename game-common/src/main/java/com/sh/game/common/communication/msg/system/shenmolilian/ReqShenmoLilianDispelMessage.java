package com.sh.game.common.communication.msg.system.shenmolilian;

import com.google.protobuf.InvalidProtocolBufferException;
import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;


/**
 * <p>请求驱散恶灵</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqShenmoLilianDispelMessage extends ProtobufMessage {

    private com.sh.game.protos.ShenmoLilianProtos.ReqShenmoLilianDispelMessage proto;

    private com.sh.game.protos.ShenmoLilianProtos.ReqShenmoLilianDispelMessage.Builder builder;

	
	@Override
	public int getId() {
		return 399004;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ShenmoLilianProtos.ReqShenmoLilianDispelMessage.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ShenmoLilianProtos.ReqShenmoLilianDispelMessage.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ShenmoLilianProtos.ReqShenmoLilianDispelMessage.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ShenmoLilianProtos.ReqShenmoLilianDispelMessage getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ShenmoLilianProtos.ReqShenmoLilianDispelMessage proto) {
        this.proto = proto;
    }

}
