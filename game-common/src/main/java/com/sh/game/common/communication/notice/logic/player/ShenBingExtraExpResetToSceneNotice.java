package com.sh.game.common.communication.notice.logic.player;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * 神兵每日额外经验重置 Notice
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2021-08-18
 **/
@Notice
@Getter
@Setter
public class ShenBingExtraExpResetToSceneNotice extends ProcessNotice {
    /**
     * 玩家编号
     */
    private long roleId;

    /**
     * 当天已累计的经验
     */
    private long todayAcquiredExp;

    public ShenBingExtraExpResetToSceneNotice() {
    }

    public ShenBingExtraExpResetToSceneNotice(long roleId, long todayAcquiredExp) {
        this.roleId = roleId;
        this.todayAcquiredExp = todayAcquiredExp;
    }
}
