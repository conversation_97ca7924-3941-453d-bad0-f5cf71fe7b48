package com.sh.game.common.communication.msg.system.transformSuperMan;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求狂暴保护状态变更</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqSuperManProtectMessage extends ProtobufMessage {

    private com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect proto;

    private com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect.Builder builder;

	
	@Override
	public int getId() {
		return 318003;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect proto) {
        this.proto = proto;
    }

}
