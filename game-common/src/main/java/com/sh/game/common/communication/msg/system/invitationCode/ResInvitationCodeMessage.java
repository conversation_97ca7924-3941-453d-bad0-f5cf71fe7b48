package com.sh.game.common.communication.msg.system.invitationCode;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>返回玩家已生成的邀请码</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResInvitationCodeMessage extends ProtobufMessage {

    private com.sh.game.protos.InvitationCodeProtos.ResInvitationCode proto;

    private com.sh.game.protos.InvitationCodeProtos.ResInvitationCode.Builder builder;

	
	@Override
	public int getId() {
		return 330002;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.InvitationCodeProtos.ResInvitationCode.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.InvitationCodeProtos.ResInvitationCode.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.InvitationCodeProtos.ResInvitationCode.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.InvitationCodeProtos.ResInvitationCode getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.InvitationCodeProtos.ResInvitationCode proto) {
        this.proto = proto;
    }

}
