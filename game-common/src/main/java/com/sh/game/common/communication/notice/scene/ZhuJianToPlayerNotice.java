package com.sh.game.common.communication.notice.scene;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * 铸剑
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2021-12-14
 **/
@Setter
@Getter
@Notice
public class ZhuJianToPlayerNotice extends ProcessNotice {

    /**
     * 角色id
     */
    private long rid;

    /**
     * 铸剑次数
     */
    private int count;

    public ZhuJianToPlayerNotice(long rid, int count) {
        this.rid = rid;
        this.count = count;
    }

    public ZhuJianToPlayerNotice() {
    }
}
