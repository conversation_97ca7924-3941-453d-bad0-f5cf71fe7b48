package com.sh.game.common.communication.msg.system.secondaryPassword;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>操作密码返回</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResSecondaryPasswordMessage extends ProtobufMessage {

    private com.sh.game.protos.SecondaryPasswordProtos.ResSecondaryPassword proto;

    private com.sh.game.protos.SecondaryPasswordProtos.ResSecondaryPassword.Builder builder;

	
	@Override
	public int getId() {
		return 280004;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.SecondaryPasswordProtos.ResSecondaryPassword.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.SecondaryPasswordProtos.ResSecondaryPassword.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.SecondaryPasswordProtos.ResSecondaryPassword.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.SecondaryPasswordProtos.ResSecondaryPassword getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.SecondaryPasswordProtos.ResSecondaryPassword proto) {
        this.proto = proto;
    }

}
