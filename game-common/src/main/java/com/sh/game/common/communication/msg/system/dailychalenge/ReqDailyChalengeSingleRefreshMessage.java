package com.sh.game.common.communication.msg.system.dailychalenge;

import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求重新刷新当前接受的任务</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqDailyChalengeSingleRefreshMessage extends ProtobufMessage {

    private com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeSingleRefresh proto;

    private com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeSingleRefresh.Builder builder;

	
	@Override
	public int getId() {
		return 306010;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeSingleRefresh.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeSingleRefresh.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeSingleRefresh.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeSingleRefresh getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeSingleRefresh proto) {
        this.proto = proto;
    }

}
