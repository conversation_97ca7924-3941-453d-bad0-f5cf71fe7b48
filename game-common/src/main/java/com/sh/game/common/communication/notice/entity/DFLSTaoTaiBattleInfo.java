package com.sh.game.common.communication.notice.entity;

import com.sh.game.common.entity.match.DFLSUnion;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/7 13:16
 */
@Getter
@Setter
public class DFLSTaoTaiBattleInfo {

    @Tag(1)
    private Map<Long,DFLSUnion> unions = new HashMap<>();

    @Tag(2)
    private long battleId;

    @Tag(3)
    private int dflsType;

    @Tag(4)
    private int dflsGroup;

    @Tag(5)
    private int index;

    @Tag(6)
    private int saiQu;

    @Tag(7)
    private long taoTaiWinUnionId;
}
