package com.sh.game.common.communication.notice.logic.player;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2022/02/28 09:28
 */
@Getter
@Setter
@Notice
public class ShenBingFreeReliveUpdateNotice extends ProcessNotice {
    /**
     * 玩家rid
     */
    private long roleId;

    /**
     * 当天剩余免费复活次数
     */
    private int freeReliveTime;
}
