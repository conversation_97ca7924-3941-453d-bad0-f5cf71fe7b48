package com.sh.game.common.communication.notice.logic.player;

import com.sh.game.common.entity.match.DFLSUnion;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/26 14:50
 */
@Getter
@Setter
@Notice
public class DFLSMatchSuccessNotice extends ProcessNotice {

    private List<DFLSUnion> unionList = new ArrayList<>();

    private int dflsType;

    private int index;

    private long battleId;

    private int saiQu;
}
