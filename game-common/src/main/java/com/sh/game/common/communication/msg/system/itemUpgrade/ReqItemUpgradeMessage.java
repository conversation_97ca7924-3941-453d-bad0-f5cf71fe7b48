package com.sh.game.common.communication.msg.system.itemUpgrade;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求道具升级</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toServer")
public class ReqItemUpgradeMessage extends ProtobufMessage {

    private com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade proto;

    private com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade.Builder builder;

	
	@Override
	public int getId() {
		return 328001;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade proto) {
        this.proto = proto;
    }

}
