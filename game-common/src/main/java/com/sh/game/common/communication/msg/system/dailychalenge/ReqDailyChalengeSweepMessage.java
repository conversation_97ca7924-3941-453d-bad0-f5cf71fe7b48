package com.sh.game.common.communication.msg.system.dailychalenge;

import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>扫荡</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqDailyChalengeSweepMessage extends ProtobufMessage {

    private com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeSweep proto;

    private com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeSweep.Builder builder;

	
	@Override
	public int getId() {
		return 306007;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeSweep.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeSweep.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeSweep.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeSweep getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeSweep proto) {
        this.proto = proto;
    }

}
