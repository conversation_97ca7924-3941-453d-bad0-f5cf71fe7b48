package com.sh.game.common.communication.notice;

import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Notice
public class MailSendNotice extends ProcessNotice {

    private long roleId;

    private String sender;

    private String title;

    private String content;

    private int mailCfgId;

    private List<Item> attachments;

    private List<Object> params;
}
