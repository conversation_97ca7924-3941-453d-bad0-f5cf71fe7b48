package com.sh.game.common.communication.msg.system.dailyBanZhuan;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求特殊换砖</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toServer")
public class ReqSpDailyBanZhuanMessage extends ProtobufMessage {

    private com.sh.game.protos.DailyBanZhuanProtos.ReqSpDailyBanZhuan proto;

    private com.sh.game.protos.DailyBanZhuanProtos.ReqSpDailyBanZhuan.Builder builder;

	
	@Override
	public int getId() {
		return 367003;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.DailyBanZhuanProtos.ReqSpDailyBanZhuan.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.DailyBanZhuanProtos.ReqSpDailyBanZhuan.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.DailyBanZhuanProtos.ReqSpDailyBanZhuan.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.DailyBanZhuanProtos.ReqSpDailyBanZhuan getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.DailyBanZhuanProtos.ReqSpDailyBanZhuan proto) {
        this.proto = proto;
    }

}
