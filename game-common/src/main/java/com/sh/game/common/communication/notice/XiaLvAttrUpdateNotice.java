package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * 侠侣属性变化通知
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2022-02-16
 **/
@Notice
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class XiaLvAttrUpdateNotice extends ProcessNotice {

    /**
     * 玩家编号
     */
    private long roleId;

    /**
     * 侠侣守护等级
     */
    private int xiaLvGuardianLevel;

    /**
     * 侠侣装备
     */
    private List<Integer> equipIds;

}

