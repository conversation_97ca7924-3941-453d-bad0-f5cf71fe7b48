package com.sh.game.common.communication.notice.match;

import com.sh.game.common.communication.msg.pvp.bean.MatchPlayerBean;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import com.sh.game.protos.PvpmatchProtos;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 返回匹配结果
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2022/8/12.
 */
@Getter
@Setter
@Notice
public class MatchInfoToLogicNotice extends ProcessNotice {

    /**
     * 请求匹配的组
     */
    private long teamId;
    /**
     * pvp类型
     */
    private int pvpType;

    private int fightPower;

    private List<PvpmatchProtos.MatchPlayerBean> players = new ArrayList<>();

    private String info;

    private List<Long> rid = new ArrayList<>();
}
