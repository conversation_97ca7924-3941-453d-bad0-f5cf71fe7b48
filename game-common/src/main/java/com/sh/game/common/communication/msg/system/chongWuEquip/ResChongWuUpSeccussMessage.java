package com.sh.game.common.communication.msg.system.chongWuEquip;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>返回强化信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResChongWuUpSeccussMessage extends ProtobufMessage {

    private com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss proto;

    private com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss.Builder builder;

	
	@Override
	public int getId() {
		return 357002;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss proto) {
        this.proto = proto;
    }

}
