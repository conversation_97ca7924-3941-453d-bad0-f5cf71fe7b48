package com.sh.game.common.communication.msg.system.monsterCard;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求怪物图鉴信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toServer")
public class ReqMonsterCardInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.MonsterCardProtos.ReqMonsterCardInfo proto;

    private com.sh.game.protos.MonsterCardProtos.ReqMonsterCardInfo.Builder builder;

	
	@Override
	public int getId() {
		return 316001;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.MonsterCardProtos.ReqMonsterCardInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.MonsterCardProtos.ReqMonsterCardInfo.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.MonsterCardProtos.ReqMonsterCardInfo.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.MonsterCardProtos.ReqMonsterCardInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.MonsterCardProtos.ReqMonsterCardInfo proto) {
        this.proto = proto;
    }

}
