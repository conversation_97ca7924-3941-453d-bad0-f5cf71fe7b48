package com.sh.game.common.communication.msg.system.militaryRank;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求领取军衔俸禄</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toServer")
public class ReqMilitaryRankDailyRewardMessage extends ProtobufMessage {

    private com.sh.game.protos.MilitaryRankProtos.ReqMilitaryRankDailyReward proto;

    private com.sh.game.protos.MilitaryRankProtos.ReqMilitaryRankDailyReward.Builder builder;

	
	@Override
	public int getId() {
		return 366005;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.MilitaryRankProtos.ReqMilitaryRankDailyReward.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.MilitaryRankProtos.ReqMilitaryRankDailyReward.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.MilitaryRankProtos.ReqMilitaryRankDailyReward.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.MilitaryRankProtos.ReqMilitaryRankDailyReward getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.MilitaryRankProtos.ReqMilitaryRankDailyReward proto) {
        this.proto = proto;
    }

}
