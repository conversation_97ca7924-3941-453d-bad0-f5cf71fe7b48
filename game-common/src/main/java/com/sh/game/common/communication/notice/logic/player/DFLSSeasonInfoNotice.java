package com.sh.game.common.communication.notice.logic.player;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/11/21 17:36
 */
@Getter
@Setter
@Notice
public class DFLSSeasonInfoNotice extends ProcessNotice {

    private long roleId;

    private byte sourceProcessorId;

    private boolean noCallback;

    private int dflsGroup;

    private int hostId;
}
