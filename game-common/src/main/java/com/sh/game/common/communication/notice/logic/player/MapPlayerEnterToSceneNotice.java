package com.sh.game.common.communication.notice.logic.player;

import com.sh.game.common.cd.CD;
import com.sh.game.common.entity.map.RoleDTO;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.*;

/**
 * 通知玩家进入场景
 */
@Notice
@Getter
@Setter
public class MapPlayerEnterToSceneNotice extends ProcessNotice {

    /**
     * 游戏服编号
     */
    private int host;

    /**
     * 玩家编号
     */
    private long roleId;

    /**
     * 地图唯一编号
     */
    private long mapId;

    /**
     * 坐标
     */
    private int x;

    /**
     * 坐标
     */
    private int y;

    /**
     * 范围
     */
    private int range;

    /**
     * 登录类型
     */
    private int enterType;

    private int deliverId;

    /**
     * 玩家数据
     */
    private RoleDTO roleDTO;

    /**
     * 队伍编号
     */
    private long teamID;

    /**
     * 当前地图需要消耗道具拥有总数
     */
    private long totalOwn;

    /**
     * 祭坛次数
     */
    private int jiTanCount;

    /**
     * cd 要传递过去
     */
    private Map<Long, CD> cdMap = new HashMap<>();


    /**
     * 战意等级
     */
    private int zhanyi;

    /**
     * 当前的神兵会员等级
     */
    private int curShenBingVipLevel;

    /**
     * 当前解锁神兵
     */
    private int maxGainExpShenBingId;

    /**
     * 当日增加的神兵特权额外经验
     */
    private long dailyAddExtraExp;

    /**
     * 神兵每日免费复活次数
     */
    private int reliveFreeTime;

    /**
     * 第一次登录携带fakeId,场景删除假人
     */
    private long fakePlayerId;

    /**
     * 已激活的装扮外观
     */
    private Set<Integer> appearanceSets = new HashSet<>();

    /**
     * 继承的免费时间
     */
    private int inheritFreeTime;

    /**
     * 觉醒等级
     */
    private Map<Integer, Integer> juexingLevel = new HashMap<>();

    /**
     * 月卡
     * key: 月卡id
     * value: 到期时间(永久为-1)
     */
    private Map<Integer, Integer> monthCard = new HashMap<>();

}
