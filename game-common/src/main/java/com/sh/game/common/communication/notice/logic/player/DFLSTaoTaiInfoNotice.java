package com.sh.game.common.communication.notice.logic.player;

import com.sh.game.common.communication.notice.entity.DFLSTaoTaiBattleInfo;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/7 13:15
 */
@Notice
@Getter
@Setter
public class DFLSTaoTaiInfoNotice extends ProcessNotice {

    private List<DFLSTaoTaiBattleInfo> battleInfos = new ArrayList<>();

}
