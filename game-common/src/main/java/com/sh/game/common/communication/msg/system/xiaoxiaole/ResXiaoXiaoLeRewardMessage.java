package com.sh.game.common.communication.msg.system.xiaoxiaole;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
* <p>推送消消乐游戏通关奖励</p>
* <p>Created by MessageUtil</p>
* <p>该类是自动生成的，不允许手动修改</p>
*/
@RPC("toClient")
public class ResXiaoXiaoLeRewardMessage extends ProtobufMessage {

    private com.sh.game.protos.XiaoXiaoLeProtos.ResXiaoXiaoLeRewardMessage proto;

    private com.sh.game.protos.XiaoXiaoLeProtos.ResXiaoXiaoLeRewardMessage.Builder builder;


    @Override
    public int getId() {
        return 510004;
    }

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.XiaoXiaoLeProtos.ResXiaoXiaoLeRewardMessage.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.XiaoXiaoLeProtos.ResXiaoXiaoLeRewardMessage.Builder newBuilder() {
        if (builder == null) {
            builder = com.sh.game.protos.XiaoXiaoLeProtos.ResXiaoXiaoLeRewardMessage.newBuilder();
        }
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.XiaoXiaoLeProtos.ResXiaoXiaoLeRewardMessage getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.XiaoXiaoLeProtos.ResXiaoXiaoLeRewardMessage proto) {
        this.proto = proto;
    }

}
