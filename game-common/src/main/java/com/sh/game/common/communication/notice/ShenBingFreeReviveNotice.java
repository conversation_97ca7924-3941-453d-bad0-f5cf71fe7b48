package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: 消耗神兵每日免费复活次数
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2022/02/25 16:09
 */
@Getter
@Setter
@Notice
public class ShenBingFreeReviveNotice extends ProcessNotice {
    private long roleId;
    private byte sourceProcessorId;
}
