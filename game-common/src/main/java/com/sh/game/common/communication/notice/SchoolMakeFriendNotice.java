package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025-01-14
 */
@Getter
@Setter
@Notice
public class SchoolMakeFriendNotice extends ProcessNotice {

    private long rid;

    private int studentId;

    private int time;

    private Map<Integer, Long> targetSecondIncome;

    private long targetRid;

    private int targetStudentId;

    private int targetStuQuality;
}
