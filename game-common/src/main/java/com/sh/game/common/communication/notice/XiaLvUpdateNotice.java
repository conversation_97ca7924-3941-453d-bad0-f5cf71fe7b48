package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Notice
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class XiaLvUpdateNotice extends ProcessNotice {

    /**
     * 玩家编号
     */
    private long roleId;

    /**
     * 侠侣配置ID
     */
    private int xialvId;


    /**
     * 侠侣使用的怪物模型（怪物表id）
     */
    private int xiaLvMonsterModel;

    /**
     * 守护等级
     */
    private int xiaLvGuardianLevel;

    private List<Integer> equipIds;

    /**
     * 侠侣最后一次死亡时间戳
     */
    private long xiaLvLastDieTime;

    /**
     * 侠侣血量
     */
    private long xiaLvHp;
}
