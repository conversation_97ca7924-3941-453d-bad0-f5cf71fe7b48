package com.sh.game.common.communication.msg.system.wanxianbang;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>返回排行</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResWanXianBangRankMessage extends ProtobufMessage {

    private com.sh.game.protos.WanXianBangProtos.ResWanXianBangRank proto;

    private com.sh.game.protos.WanXianBangProtos.ResWanXianBangRank.Builder builder;

	
	@Override
	public int getId() {
		return 396009;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.WanXianBangProtos.ResWanXianBangRank.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.WanXianBangProtos.ResWanXianBangRank.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.WanXianBangProtos.ResWanXianBangRank.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.WanXianBangProtos.ResWanXianBangRank getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.WanXianBangProtos.ResWanXianBangRank proto) {
        this.proto = proto;
    }

}
