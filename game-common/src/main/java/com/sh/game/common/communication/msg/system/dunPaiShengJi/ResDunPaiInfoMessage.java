package com.sh.game.common.communication.msg.system.dunPaiShengJi;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import com.sh.game.common.communication.msg.system.dunPaiShengJi.bean.SpecialUpBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>返回盾牌升级</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toClient")
public class ResDunPaiInfoMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 349002;
	}
	
	/**
	 * 盾牌等级
	 */
	private List<SpecialUpBean> dunPaiBean = new ArrayList<>();

	public List<SpecialUpBean> getDunPaiBean() {
		return dunPaiBean;
	}

	public void setDunPaiBean(List<SpecialUpBean> dunPaiBean) {
		this.dunPaiBean = dunPaiBean;
	}

	@Override
	public boolean read(KryoInput buf) {

		int dunPaiBeanLength = readShort(buf);
		for (int dunPaiBeanI = 0; dunPaiBeanI < dunPaiBeanLength; dunPaiBeanI++) {
			if (readByte(buf) == 0) { 
				this.dunPaiBean.add(null);
			} else {
				SpecialUpBean specialUpBean = new SpecialUpBean();
				specialUpBean.read(buf);
				this.dunPaiBean.add(specialUpBean);
			}
		}
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		writeShort(buf, this.dunPaiBean.size());
		for (int dunPaiBeanI = 0; dunPaiBeanI < this.dunPaiBean.size(); dunPaiBeanI++) {
			this.writeBean(buf, this.dunPaiBean.get(dunPaiBeanI));
		}
		return true;
	}
}
