package com.sh.game.common.communication.msg.system.dailychalenge;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>进度变化</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResDailyChalengeChangeMessage extends ProtobufMessage {

    private com.sh.game.protos.DailychalengeProtos.ResDailyChalengeChange proto;

    private com.sh.game.protos.DailychalengeProtos.ResDailyChalengeChange.Builder builder;

	
	@Override
	public int getId() {
		return 306003;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.DailychalengeProtos.ResDailyChalengeChange.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.DailychalengeProtos.ResDailyChalengeChange.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.DailychalengeProtos.ResDailyChalengeChange.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.DailychalengeProtos.ResDailyChalengeChange getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.DailychalengeProtos.ResDailyChalengeChange proto) {
        this.proto = proto;
    }

}
