package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * 战意等级更新notice
 */
@Notice
@Getter
@Setter
public class ZhanYiUpdateNotice extends ProcessNotice {

    /**
     * 玩家编号
     */
    private long roleId;

    /**
     * 战意等级
     */
    private int zhanyiLv;

    public ZhanYiUpdateNotice() {
    }

    public ZhanYiUpdateNotice(long roleId, int zhanyiLv) {
        this.roleId = roleId;
        this.zhanyiLv = zhanyiLv;
     }
}
