package com.sh.game.common.communication.msg.system.unionShenHuang;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求解锁特戒灵石槽位</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqUnionShenHuangUpMessage extends ProtobufMessage {

    private com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp proto;

    private com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp.Builder builder;

	
	@Override
	public int getId() {
		return 361003;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp proto) {
        this.proto = proto;
    }

}
