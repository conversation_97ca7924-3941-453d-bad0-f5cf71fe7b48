package com.sh.game.common.communication.msg.system.shenBingHuiYuan;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

/**
 * 请求神兵特权激活状态
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2021-07-23
 **/
@RPC("toLogic")
public class ReqShenBingStatusMessage extends AbstractMessage {
    @Override
    public int getId() {
        return 200005;
    }


    @Override
    public boolean read(KryoInput buf) {

        return true;
    }

    @Override
    public boolean write(KryoOutput buf) {

        return true;
    }
}
