package com.sh.game.common.communication.msg.system.shenBingHuiYuan;

import com.sh.game.common.communication.msg.abc.bean.CommonItemBean;
import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import java.util.ArrayList;
import java.util.List;


/**
 * <p>响应幸运掉落弹窗</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toLogic")
public class ResLuckDropPanelMessage extends AbstractMessage {

	@Override
	public int getId() {
		return 200019;
	}

	/**
	 * 展示道具
	 */
	private List<CommonItemBean> items = new ArrayList<>();
	/**
	 * 1: 幸运掉落大, 2: 幸运掉落小
	 */
	private int type;

	public List<CommonItemBean> getItems() {
		return items;
	}

	public void setItems(List<CommonItemBean> items) {
		this.items = items;
	}
	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}


	@Override
	public boolean read(KryoInput buf) {

		int itemsLength = readShort(buf);
		for (int itemsI = 0; itemsI < itemsLength; itemsI++) {
			if (readByte(buf) == 0) {
				this.items.add(null);
			} else {
				CommonItemBean commonItemBean = new CommonItemBean();
				commonItemBean.read(buf);
				this.items.add(commonItemBean);
			}
		}
		this.type = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		writeShort(buf, this.items.size());
		for (int itemsI = 0; itemsI < this.items.size(); itemsI++) {
			this.writeBean(buf, this.items.get(itemsI));
		}
		this.writeInt(buf, type, false);
		return true;
	}
}

