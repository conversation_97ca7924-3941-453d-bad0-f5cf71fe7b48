package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/21 16:14
 */
@Getter
@Setter
@Notice
public class HuangChengLogNotice extends ProcessNotice {

    private long roleId;

    private String name;

    private int win;

    private int time;

    private int level;

    private Map<Integer, Integer> fashions = new HashMap<>();

    private long targetRid;
}
