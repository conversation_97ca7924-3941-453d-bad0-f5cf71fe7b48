package com.sh.game.common.communication.msg.system.zhuansheng;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求转生</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqZhuanshengMessage extends ProtobufMessage {

    private com.sh.game.protos.ZhuanshengProtos.ReqZhuansheng proto;

    private com.sh.game.protos.ZhuanshengProtos.ReqZhuansheng.Builder builder;

	
	@Override
	public int getId() {
		return 208003;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ZhuanshengProtos.ReqZhuansheng.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ZhuanshengProtos.ReqZhuansheng.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ZhuanshengProtos.ReqZhuansheng.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ZhuanshengProtos.ReqZhuansheng getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ZhuanshengProtos.ReqZhuansheng proto) {
        this.proto = proto;
    }

}
