package com.sh.game.common.communication.msg.system.shenBingHuiYuan;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>请求领取神兵解锁奖励</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toLogic")
public class ReqShenBingRewardMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 200009;
	}
	
	/**
	 * 神兵特权cfgId
	 */
	private int shenBingCfgId;

	public int getShenBingCfgId() {
		return shenBingCfgId;
	}

	public void setShenBingCfgId(int shenBingCfgId) {
		this.shenBingCfgId = shenBingCfgId;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.shenBingCfgId = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, shenBingCfgId, false);
		return true;
	}
}
