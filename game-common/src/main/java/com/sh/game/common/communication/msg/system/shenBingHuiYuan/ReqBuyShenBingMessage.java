package com.sh.game.common.communication.msg.system.shenBingHuiYuan;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

/**
 * 请求购买神兵会员
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2021-08-11
 **/
@RPC("toLogic")
public class ReqBuyShenBingMessage extends AbstractMessage {
    @Override
    public int getId() {
        return 200014;
    }

    /**
     * 待购买的神兵 cfgId
     */
    private int shenBingCfgId;

    public int getShenBingCfgId() {
        return shenBingCfgId;
    }

    public void setShenBingCfgId(int shenBingCfgId) {
        this.shenBingCfgId = shenBingCfgId;
    }

    @Override
    public boolean read(KryoInput buf) {

        this.shenBingCfgId = readInt(buf, false);
        return true;
    }

    @Override
    public boolean write(KryoOutput buf) {

        this.writeInt(buf, shenBingCfgId, false);
        return true;
    }
}
