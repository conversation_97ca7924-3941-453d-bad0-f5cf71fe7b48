package com.sh.game.common.communication.msg.system.dailychalenge;

import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>一键完成每日挑战</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqDailyChalengeAllFinishMessage extends ProtobufMessage {

    private com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeAllFinish proto;

    private com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeAllFinish.Builder builder;

	
	@Override
	public int getId() {
		return 306009;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeAllFinish.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeAllFinish.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeAllFinish.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeAllFinish getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.DailychalengeProtos.ReqDailyChalengeAllFinish proto) {
        this.proto = proto;
    }

}
