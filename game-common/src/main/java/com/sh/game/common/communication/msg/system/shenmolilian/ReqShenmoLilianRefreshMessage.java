package com.sh.game.common.communication.msg.system.shenmolilian;

import com.google.protobuf.InvalidProtocolBufferException;
import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;


/**
 * <p>请求刷新恶灵</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqShenmoLilianRefreshMessage extends ProtobufMessage {

    private com.sh.game.protos.ShenmoLilianProtos.ReqShenmoLilianRefreshMessage proto;

    private com.sh.game.protos.ShenmoLilianProtos.ReqShenmoLilianRefreshMessage.Builder builder;

	
	@Override
	public int getId() {
		return 399003;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ShenmoLilianProtos.ReqShenmoLilianRefreshMessage.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ShenmoLilianProtos.ReqShenmoLilianRefreshMessage.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ShenmoLilianProtos.ReqShenmoLilianRefreshMessage.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ShenmoLilianProtos.ReqShenmoLilianRefreshMessage getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ShenmoLilianProtos.ReqShenmoLilianRefreshMessage proto) {
        this.proto = proto;
    }

}
