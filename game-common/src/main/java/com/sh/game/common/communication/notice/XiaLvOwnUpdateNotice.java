package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * 拥有的侠侣列表
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2021-12-17
 **/
@Notice
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class XiaLvOwnUpdateNotice extends ProcessNotice {
    /**
     * 玩家编号
     */
    private long roleId;

    /**
     * 拥有的侠侣
     */
    private Map<Integer, Integer> xiaLvCidByLevelMap = new HashMap<>();
}
