package com.sh.game.common.communication.msg.system.dunPaiShengJi;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>请求盾牌升级</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toLogic")
public class ReqDunPaiUpMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 349001;
	}
	
	/**
	 * 升级类型,1时装升级，2盾牌升级
	 */
	private int type;

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.type = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, type, false);
		return true;
	}
}
