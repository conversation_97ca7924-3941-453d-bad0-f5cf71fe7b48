package com.sh.game.common.communication.msg.system.shenBingHuiYuan;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>发送每日复活次数</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toClient")
public class ResShenBingReliveFreeTimeMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 200018;
	}
	
	/**
	 * 剩余免费复活次数
	 */
	private int freeTime;

	public int getFreeTime() {
		return freeTime;
	}

	public void setFreeTime(int freeTime) {
		this.freeTime = freeTime;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.freeTime = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, freeTime, false);
		return true;
	}
}
