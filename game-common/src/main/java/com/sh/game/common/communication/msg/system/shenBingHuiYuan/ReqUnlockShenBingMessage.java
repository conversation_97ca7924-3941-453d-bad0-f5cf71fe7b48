package com.sh.game.common.communication.msg.system.shenBingHuiYuan;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

/**
 * 请求解锁神兵特权
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2021-07-23
 **/
@RPC("toLogic")
public class ReqUnlockShenBingMessage extends AbstractMessage {
    @Override
    public int getId() {
        return 200006;
    }

    /**
     * 待解锁神兵 cfgId
     */
    private int shenBingCfgId;

    public int getShenBingCfgId() {
        return shenBingCfgId;
    }

    public void setShenBingCfgId(int shenBingCfgId) {
        this.shenBingCfgId = shenBingCfgId;
    }

    @Override
    public boolean read(KryoInput buf) {

        this.shenBingCfgId = readInt(buf,false);
        return true;
    }

    @Override
    public boolean write(KryoOutput buf) {

        this.writeInt(buf, shenBingCfgId,false);
        return true;
    }
}
