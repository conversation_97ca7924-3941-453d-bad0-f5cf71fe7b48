package com.sh;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.cache.MapConditionCache;
import com.sh.game.common.config.model.DeliverConfig;
import com.sh.game.common.config.model.MapConditionConfig;
import com.sh.game.common.config.model.MapConfig;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 测试mapcondition 配置出错问题
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by Silence on 2021/9/17.
 */
public class CheckMapConditionTest extends ChenkExcelProcessTest {


    /**
     * @throws Exception
     */
    @Test
    public void checkDeliverCondition() throws Exception {

        //根据传送搜索

        List<DeliverConfig> deliverConfigs = ConfigDataManager.getInstance().getList(DeliverConfig.class);
        deliverConfigs = deliverConfigs.stream().filter(confg -> confg.getMapcondition() > 0).collect(Collectors.toList());
        for (DeliverConfig deliverConfig : deliverConfigs) {
            MapConditionConfig config = ConfigDataManager.getInstance().getById(MapConditionConfig.class,
                    deliverConfig.getMapcondition());


            if (config == null || config.getCondition() == null) {
                Assert.fail("DeliverConfig 配置表有错误id=" + deliverConfig.getId());
            }
        }




    }


    @Test
    public void checkMapCondition() {


        List<MapConfig> mapConfigs = ConfigDataManager.getInstance().getList(MapConfig.class);

        for (MapConfig mapConfig : mapConfigs) {
            MapConditionCache cache = ConfigCacheManager.getInstance().getCache(MapConditionCache.class);

            List<MapConditionConfig> mapConditionConfigList = cache.getList(mapConfig.getId());

            for (MapConditionConfig config : mapConditionConfigList) {
                if (config == null) {
                    Assert.fail("地图相关错误,mapId=" + mapConfig.getId() + "MapConditionConfig-Id" + config.getId());
                }
            }
        }

    }


}
