package com.sh;

import com.google.common.base.Strings;
import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.annotation.ConfigDataCustomization;
import com.sh.game.common.config.check.ConfigLoadChecker;
import org.junit.Before;

/**
 * 上传流程检测 测试用例
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by Silence on 2021/9/17.
 */
public abstract class ChenkExcelProcessTest {

    @Before
    public  void loadData() throws Exception {
        String dataPath = System.getProperty("test.data.path");
        if (Strings.isNullOrEmpty(dataPath)) {
            System.out.println("数据目录输入有误：{" + dataPath + "}");
            return;
        }

        System.out.println("check数据结构path:" + dataPath);
        ConfigDataManager.getInstance().init(
                ConfigDataCustomization.newInstance()
                        .setPkg("com.sh.game.common.config")
                        .setSkipLine(2)
                        .setChecker(ConfigLoadChecker.getInstance())
                        .setPath(dataPath));

    }
}
